"""
Bug修复测试 - 第二部分
测试修复的功能点：
1. session过期和登出问题
2. 日历时间显示问题
3. 日历房间名称显示问题
4. 新建预约模板显示问题
"""

import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from core.models import User, Room, RoomState, RoomParticipant, SystemTemplate
from events.models import EventTemplate, EventStep


class BugFixesTestCase(TestCase):
    """Bug修复测试基类"""
    
    def setUp(self):
        """测试准备"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='testuser1',
            password='testpassword1',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.user2 = User.objects.create_user(
            username='testuser2',
            password='testpassword2',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='Test Description',
            creator=self.user1
        )
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='System Template',
            description='System Template Description',
            is_active=True,
            required_subscription='FREE'
        )
        
        # 创建API客户端
        self.client = APIClient()
        
    def authenticate_user(self, user):
        """认证用户"""
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')


class SessionExpiryTest(BugFixesTestCase):
    """测试会话过期和登出功能"""

    def test_session_expiry_response(self):
        """测试会话过期响应格式"""
        # 使用无效token访问需要认证的API
        response = self.client.get('/api/calendar/reservations/', HTTP_AUTHORIZATION='Bearer invalid_token')

        # 注意：在测试环境中，API可能返回403而不是401
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])
        data = response.json()

        # 验证响应包含错误信息（DRF标准格式）
        self.assertTrue('detail' in data or 'error' in data)

        # 验证token无效的响应
        if 'code' in data:
            self.assertEqual(data['code'], 'token_not_valid')


class RoomNameTest(BugFixesTestCase):
    """测试房间名称显示功能"""
    
    def test_room_name_in_calendar_data(self):
        """测试日历数据中的房间名称"""
        self.authenticate_user(self.user1)
        
        # 创建预约房间
        future_time = timezone.now() + timedelta(hours=3)
        room_name = "测试预约房间名称"
        
        # 创建预约请求
        schedule_data = {
            'name': room_name,
            'template_id': f'system_{self.system_template.id}',
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        # 发送预约请求
        response = self.client.post(
            '/api/rooms/schedule/',
            data=json.dumps(schedule_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 获取日历数据
        response = self.client.get('/api/calendar/reservations/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('reservations', data)
        self.assertGreater(len(data['reservations']), 0)
        
        # 验证房间名称正确显示
        reservation = data['reservations'][0]
        self.assertEqual(reservation['name'], room_name)


class CalendarDateTimeFormatTest(BugFixesTestCase):
    """测试日历日期时间格式"""
    
    def test_calendar_data_datetime_format(self):
        """测试日历数据中的日期时间格式"""
        self.authenticate_user(self.user1)
        
        # 创建跨天的预约房间
        start_time = timezone.now() + timedelta(hours=3)
        end_time = start_time + timedelta(hours=24)  # 跨天
        
        room = Room.objects.create(
            room_code='TEST01',
            name='测试跨天房间',
            host=self.user1,
            status=RoomState.SCHEDULED,
            scheduled_start_time=start_time,
            duration_hours=24,
            system_template=self.system_template
        )
        
        # 获取日历数据
        response = self.client.get('/api/calendar/reservations/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('reservations', data)
        self.assertGreater(len(data['reservations']), 0)
        
        # 验证开始和结束时间格式
        reservation = data['reservations'][0]
        self.assertIn('start_time', reservation)
        self.assertIn('end_time', reservation)
        
        # 验证ISO格式
        start_time_str = reservation['start_time']
        end_time_str = reservation['end_time']
        
        # 尝试解析ISO格式
        try:
            datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
        except ValueError:
            self.fail("日期时间格式不是有效的ISO格式")


class TemplateDisplayTest(BugFixesTestCase):
    """测试模板显示功能"""
    
    def test_room_templates_api(self):
        """测试房间模板API返回格式"""
        self.authenticate_user(self.user1)
        
        # 获取房间模板
        response = self.client.get('/api/room-templates/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('templates', data)
        self.assertIn('total_count', data)
        
        templates = data['templates']
        self.assertGreaterEqual(len(templates), 1)  # 至少有一个模板
        
        # 验证模板格式
        template = templates[0]
        required_fields = ['id', 'name', 'description', 'type']
        for field in required_fields:
            self.assertIn(field, template)
        
        # 验证系统模板和用户模板分类
        system_templates = [t for t in templates if t['type'] == 'system']
        user_templates = [t for t in templates if t['type'] == 'user']
        
        # 验证至少有一个系统模板
        self.assertGreaterEqual(len(system_templates), 1)
        
        # 验证用户模板
        if user_templates:
            user_template = user_templates[0]
            self.assertEqual(user_template['creator_username'], self.user1.username)

    def test_template_name_not_undefined(self):
        """测试模板名称不为undefined"""
        self.authenticate_user(self.user1)

        # 获取房间模板
        response = self.client.get('/api/room-templates/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        templates = data['templates']

        # 验证所有模板都有有效的名称
        for template in templates:
            self.assertIsNotNone(template.get('name'))
            self.assertNotEqual(template.get('name'), '')
            self.assertNotEqual(template.get('name'), 'undefined')
            self.assertNotEqual(template.get('name'), 'null')


class RoomModelTest(BugFixesTestCase):
    """测试Room模型的name字段"""

    def test_room_name_field_exists(self):
        """测试Room模型包含name字段"""
        room = Room.objects.create(
            room_code='TEST02',
            name='测试房间名称',
            host=self.user1,
            status=RoomState.OPEN
        )

        # 验证name字段存在且可以设置
        self.assertEqual(room.name, '测试房间名称')

        # 验证可以更新name字段
        room.name = '更新后的房间名称'
        room.save()

        # 重新获取并验证
        room.refresh_from_db()
        self.assertEqual(room.name, '更新后的房间名称')

    def test_scheduled_room_with_name(self):
        """测试预约房间包含名称"""
        future_time = timezone.now() + timedelta(hours=3)

        room = Room.objects.create(
            room_code='SCHED01',
            name='预约房间测试',
            host=self.user1,
            status=RoomState.SCHEDULED,
            scheduled_start_time=future_time,
            duration_hours=2,
            system_template=self.system_template
        )

        # 验证预约房间的名称
        self.assertEqual(room.name, '预约房间测试')
        self.assertEqual(room.status, RoomState.SCHEDULED)
        self.assertIsNotNone(room.scheduled_start_time)


class AuthenticationMiddlewareTest(BugFixesTestCase):
    """测试认证中间件"""

    def test_invalid_token_response(self):
        """测试无效token的响应"""
        # 使用无效token访问需要认证的API
        response = self.client.get('/api/calendar/reservations/', HTTP_AUTHORIZATION='Bearer invalid_token')

        # 在测试环境中，可能返回403而不是401
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])
        data = response.json()

        # 验证响应格式（DRF标准格式）
        self.assertTrue('detail' in data or 'error' in data)

    def test_missing_token_response(self):
        """测试缺少token的响应"""
        # 不设置认证头，访问需要认证的API
        response = self.client.get('/api/calendar/reservations/')

        # 在测试环境中，可能返回403而不是401
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])
        data = response.json()

        # 验证响应格式（DRF标准格式）
        self.assertTrue('detail' in data or 'error' in data)
