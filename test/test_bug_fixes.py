"""
测试bug修复的相关功能
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from core.models import Room, SystemTemplate, RoomParticipant, RoomState
from core.utils import advance_to_next_step_sync, StepInfo
# from core.views import CreateRoomView, ScheduleRoomView  # 不需要直接导入视图类
from events.models import EventTemplate, EventStep
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch
import json

User = get_user_model()


class TestSystemTemplateStepHandling(TestCase):
    """测试系统模板步骤处理功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='测试系统模板',
            description='用于测试的系统模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '自由讨论',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 300,
                        'configuration': {}
                    },
                    {
                        'id': 'step_2',
                        'name': '你画我猜',
                        'step_type': 'GAME_PICTIONARY',
                        'order': 2,
                        'duration': 600,
                        'configuration': {'rounds': 5}
                    }
                ]
            }
        )
        
        # 创建使用系统模板的房间
        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            system_template=self.system_template,
            status=RoomState.READY,
            current_step_order=0
        )
        
        # 添加房主为参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user,
            role=RoomParticipant.ROLE_HOST,
            state='JOINED'
        )

    def test_advance_to_next_step_with_system_template(self):
        """测试系统模板房间的步骤推进"""
        # 推进到第一个步骤
        next_step = advance_to_next_step_sync(self.room)
        
        # 验证返回的步骤信息
        self.assertIsNotNone(next_step)
        self.assertIsInstance(next_step, StepInfo)
        self.assertEqual(next_step.order, 1)
        self.assertEqual(next_step.step_type, 'FREE_CHAT')
        self.assertEqual(next_step.name, '自由讨论')
        self.assertEqual(next_step.duration, 300)
        
        # 验证房间状态更新
        self.room.refresh_from_db()
        self.assertEqual(self.room.current_step_order, 1)
        self.assertEqual(self.room.status, RoomState.IN_PROGRESS)

    def test_advance_to_second_step(self):
        """测试推进到第二个步骤"""
        # 先推进到第一个步骤
        self.room.current_step_order = 1
        self.room.save()
        
        # 推进到第二个步骤
        next_step = advance_to_next_step_sync(self.room)
        
        # 验证返回的步骤信息
        self.assertIsNotNone(next_step)
        self.assertEqual(next_step.order, 2)
        self.assertEqual(next_step.step_type, 'GAME_PICTIONARY')
        self.assertEqual(next_step.name, '你画我猜')
        self.assertEqual(next_step.duration, 600)
        self.assertEqual(next_step.configuration, {'rounds': 5})

    def test_advance_beyond_last_step(self):
        """测试超出最后一个步骤时的处理"""
        # 设置为最后一个步骤
        self.room.current_step_order = 2
        self.room.save()
        
        # 尝试推进
        next_step = advance_to_next_step_sync(self.room)
        
        # 应该返回None，表示没有更多步骤
        self.assertIsNone(next_step)
        
        # 房间状态应该变为ENDED
        self.room.refresh_from_db()
        self.assertEqual(self.room.status, RoomState.ENDED)

    def test_advance_step_without_system_template(self):
        """测试没有系统模板的房间推进步骤"""
        # 创建没有模板的房间
        room_without_template = Room.objects.create(
            room_code='TEST02',
            host=self.user,
            status=RoomState.READY,
            current_step_order=0
        )
        
        # 尝试推进步骤
        next_step = advance_to_next_step_sync(room_without_template)
        
        # 应该返回None
        self.assertIsNone(next_step)


class TestTemplateIDHandling(TestCase):
    """测试模板ID处理功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='测试系统模板',
            description='用于测试的系统模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )
        
        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='测试用户模板',
            description='用于测试的用户模板',
            creator=self.user
        )
        
        # 登录用户
        self.client.force_authenticate(user=self.user)

    def test_create_room_with_system_template(self):
        """测试使用系统模板创建房间"""
        response = self.client.post('/api/rooms/create/', {
            'template_id': f'system_{self.system_template.id}'
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建成功
        room_data = response.json()
        room = Room.objects.get(room_code=room_data['room_code'])
        self.assertEqual(room.system_template, self.system_template)
        self.assertIsNone(room.event_template)

    def test_create_room_with_user_template(self):
        """测试使用用户模板创建房间"""
        response = self.client.post('/api/rooms/create/', {
            'template_id': f'user_{self.user_template.id}'
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建成功
        room_data = response.json()
        room = Room.objects.get(room_code=room_data['room_code'])
        self.assertEqual(room.event_template, self.user_template)
        self.assertIsNone(room.system_template)

    def test_schedule_room_with_system_template(self):
        """测试使用系统模板预约房间"""
        from datetime import datetime, timedelta
        from django.utils import timezone
        
        scheduled_time = timezone.now() + timedelta(hours=1)
        
        response = self.client.post('/api/rooms/schedule/', {
            'name': '测试预约房间',
            'template_id': f'system_{self.system_template.id}',
            'scheduled_start_time': scheduled_time.isoformat(),
            'duration_hours': 2
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建成功
        room_data = response.json()
        room = Room.objects.get(room_code=room_data['room_code'])
        self.assertEqual(room.system_template, self.system_template)
        self.assertIsNone(room.event_template)
        self.assertEqual(room.status, RoomState.SCHEDULED)

    def test_invalid_template_id_format(self):
        """测试无效的模板ID格式"""
        response = self.client.post('/api/rooms/create/', {
            'template_id': 'invalid_format'
        })
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_nonexistent_template_id(self):
        """测试不存在的模板ID"""
        response = self.client.post('/api/rooms/create/', {
            'template_id': 'system_99999'
        })
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestRoomTemplateAPI(TestCase):
    """测试房间模板API"""
    
    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='系统模板1',
            description='系统模板描述',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '自由讨论',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 300
                    }
                ]
            }
        )
        
        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='用户模板1',
            description='用户模板描述',
            creator=self.user
        )
        
        # 登录用户
        self.client.force_authenticate(user=self.user)

    def test_get_room_templates(self):
        """测试获取房间模板列表"""
        response = self.client.get('/api/room-templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('templates', data)
        self.assertIn('total_count', data)
        
        templates = data['templates']
        self.assertGreaterEqual(len(templates), 2)  # 至少有系统模板和用户模板
        
        # 验证系统模板格式
        system_templates = [t for t in templates if t['type'] == 'system']
        self.assertGreater(len(system_templates), 0)
        
        system_template = system_templates[0]
        self.assertIn('id', system_template)
        self.assertIn('name', system_template)
        self.assertIn('description', system_template)
        self.assertIn('type', system_template)
        self.assertIn('steps', system_template)
        self.assertTrue(system_template['id'].startswith('system_'))
        
        # 验证用户模板格式
        user_templates = [t for t in templates if t['type'] == 'user']
        self.assertGreater(len(user_templates), 0)
        
        user_template = user_templates[0]
        self.assertIn('id', user_template)
        self.assertIn('name', user_template)
        self.assertIn('description', user_template)
        self.assertIn('type', user_template)
        self.assertTrue(user_template['id'].startswith('user_'))
