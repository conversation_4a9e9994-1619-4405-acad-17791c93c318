import React, { useState, useEffect, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { AppNavigator } from './AppNavigator';
import { AuthNavigator } from './AuthNavigator';
import { View, Text, StyleSheet } from 'react-native';
import { authManager } from '../utils/authManager';
import { useNavigation } from '@react-navigation/native';

export const RootNavigator = () => {
  const { user, restoreUser, logout } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const navigationRef = useRef(null);

  useEffect(() => {
    const prepare = async () => {
      await restoreUser();
      setIsReady(true);
    };
    prepare();
  }, [restoreUser]);

  // 设置authManager的认证过期回调
  useEffect(() => {
    if (isReady) {
      // 设置认证过期回调，直接调用AuthContext的logout
      authManager.setOnAuthExpired(() => {
        logout();
      });

      // 启动会话状态检查
      authManager.startSessionCheck();

      return () => {
        // 组件卸载时停止会话检查
        authManager.stopSessionCheck();
      };
    }
  }, [isReady, logout]);

  if (!isReady) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <NavigationContainer ref={navigationRef}>
      {user ? <AppNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
