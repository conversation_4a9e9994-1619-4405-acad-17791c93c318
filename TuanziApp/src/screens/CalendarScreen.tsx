// TuanziApp/src/screens/CalendarScreen.tsx

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList, CalendarData, Reservation } from '../types';
import { getMonthCalendarData } from '../api/calendarApi';
import { theme } from '../styles/theme';

type CalendarScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Calendar'>;

interface CalendarScreenProps {}

export const CalendarScreen: React.FC<CalendarScreenProps> = () => {
  const navigation = useNavigation<CalendarScreenNavigationProp>();
  const [calendarData, setCalendarData] = useState<CalendarData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());

  // 加载日历数据
  const loadCalendarData = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      
      const data = await getMonthCalendarData(year, month);
      setCalendarData(data);
    } catch (error) {
      console.error('Failed to load calendar data:', error);
      Alert.alert('错误', '加载日历数据失败，请重试');
    } finally {
      if (showLoading) setLoading(false);
    }
  }, [currentDate]);

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCalendarData(false);
    setRefreshing(false);
  }, [loadCalendarData]);

  // 页面聚焦时重新加载数据
  useFocusEffect(
    useCallback(() => {
      loadCalendarData();
    }, [loadCalendarData])
  );

  // 切换月份
  const changeMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  // 处理日期点击
  const handleDatePress = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    // 使用push而不是navigate，这样会创建一个新的导航记录
    // 当用户在ScheduleRoom页面按返回时，会直接回到Calendar页面
    navigation.push('ScheduleRoom', {
      selectedDate: dateString,
      fromCalendar: true // 标记来源是日历页面
    });
  };

  // 渲染月份导航
  const renderMonthNavigation = () => {
    const monthNames = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    
    return (
      <View style={styles.monthNavigation}>
        <TouchableOpacity 
          style={styles.navButton} 
          onPress={() => changeMonth('prev')}
        >
          <Text style={styles.navButtonText}>‹</Text>
        </TouchableOpacity>
        
        <Text style={styles.monthTitle}>
          {currentDate.getFullYear()}年 {monthNames[currentDate.getMonth()]}
        </Text>
        
        <TouchableOpacity 
          style={styles.navButton} 
          onPress={() => changeMonth('next')}
        >
          <Text style={styles.navButtonText}>›</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染日历网格
  const renderCalendarGrid = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // 获取当月第一天是星期几 (0=周日, 1=周一, ...)
    const firstDayOfWeek = firstDay.getDay();
    
    // 生成日历网格
    const days = [];
    
    // 添加上个月的日期填充
    for (let i = 0; i < firstDayOfWeek; i++) {
      const prevDate = new Date(year, month, -firstDayOfWeek + i + 1);
      days.push({ date: prevDate, isCurrentMonth: false });
    }
    
    // 添加当月的日期
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(year, month, day);
      days.push({ date, isCurrentMonth: true });
    }
    
    // 添加下个月的日期填充 (确保总共42个格子，6行7列)
    const remainingDays = 42 - days.length;
    for (let day = 1; day <= remainingDays; day++) {
      const nextDate = new Date(year, month + 1, day);
      days.push({ date: nextDate, isCurrentMonth: false });
    }

    return (
      <View style={styles.calendarGrid}>
        {/* 星期标题 */}
        <View style={styles.weekHeader}>
          {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
            <Text key={day} style={styles.weekHeaderText}>{day}</Text>
          ))}
        </View>
        
        {/* 日期网格 */}
        <View style={styles.daysGrid}>
          {days.map((dayInfo, index) => {
            const { date, isCurrentMonth } = dayInfo;
            const dateString = date.toISOString().split('T')[0];
            
            // 查找该日期的预约
            const dayReservations = calendarData?.reservations.filter(reservation => {
              const reservationDate = new Date(reservation.start_time).toISOString().split('T')[0];
              return reservationDate === dateString;
            }) || [];
            
            const hasReservations = dayReservations.length > 0;
            const isToday = dateString === new Date().toISOString().split('T')[0];
            const isPast = date < new Date(new Date().toISOString().split('T')[0]);
            
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dayCell,
                  !isCurrentMonth && styles.dayCell_otherMonth,
                  isToday && styles.dayCell_today,
                  hasReservations && styles.dayCell_hasReservations,
                  isPast && styles.dayCell_past,
                ]}
                onPress={() => isCurrentMonth && !isPast && handleDatePress(date)}
                disabled={!isCurrentMonth || isPast}
              >
                <Text style={[
                  styles.dayText,
                  !isCurrentMonth && styles.dayText_otherMonth,
                  isToday && styles.dayText_today,
                  isPast && styles.dayText_past,
                ]}>
                  {date.getDate()}
                </Text>
                {hasReservations && (
                  <View style={styles.reservationIndicator}>
                    <Text style={styles.reservationCount}>{dayReservations.length}</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // 渲染预约列表
  const renderReservationsList = () => {
    if (!calendarData?.reservations.length) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>本月暂无预约</Text>
        </View>
      );
    }

    return (
      <View style={styles.reservationsList}>
        <Text style={styles.sectionTitle}>本月预约</Text>
        {calendarData.reservations.map((reservation) => (
          <ReservationItem key={reservation.id} reservation={reservation} />
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>加载日历数据...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {renderMonthNavigation()}
      {renderCalendarGrid()}
      {renderReservationsList()}
      
      {/* 添加预约按钮 */}
      <TouchableOpacity 
        style={styles.addButton}
        onPress={() => navigation.navigate('ScheduleRoom', {})}
      >
        <Text style={styles.addButtonText}>+ 新建预约</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

// 预约项组件
const ReservationItem: React.FC<{ reservation: Reservation }> = ({ reservation }) => {
  const startTime = new Date(reservation.start_time);
  const endTime = new Date(reservation.end_time);

  // 格式化时间显示，包含完整的年月日时分信息
  const formatDateTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  };

  // 检查是否跨天
  const isCrossDay = startTime.toDateString() !== endTime.toDateString();

  return (
    <View style={styles.reservationItem}>
      <View style={styles.reservationHeader}>
        <Text style={styles.reservationName}>{reservation.name}</Text>
        <Text style={styles.reservationHost}>房主: {reservation.host_username}</Text>
      </View>
      <View style={styles.reservationTimeContainer}>
        <Text style={styles.reservationTimeLabel}>开始时间:</Text>
        <Text style={styles.reservationTime}>{formatDateTime(startTime)}</Text>
      </View>
      <View style={styles.reservationTimeContainer}>
        <Text style={styles.reservationTimeLabel}>结束时间:</Text>
        <Text style={styles.reservationTime}>
          {isCrossDay ? formatDateTime(endTime) : `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`}
        </Text>
      </View>
      <Text style={styles.reservationParticipants}>
        参与者: {reservation.current_participants}/{reservation.max_participants}
      </Text>
      {isCrossDay && (
        <Text style={styles.crossDayIndicator}>⚠️ 跨天预约</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
  },
  navButtonText: {
    fontSize: 24,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  monthTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  calendarGrid: {
    backgroundColor: theme.colors.surface,
    margin: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  weekHeader: {
    flexDirection: 'row',
    backgroundColor: theme.colors.primary,
  },
  weekHeaderText: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 12,
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayCell: {
    width: '14.28%', // 100% / 7 days
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: theme.colors.border,
    position: 'relative',
  },
  dayCell_otherMonth: {
    backgroundColor: theme.colors.gray200,
  },
  dayCell_today: {
    backgroundColor: theme.colors.primaryLight,
  },
  dayCell_hasReservations: {
    backgroundColor: theme.colors.success + '20',
  },
  dayCell_past: {
    backgroundColor: theme.colors.gray200,
  },
  dayText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  dayText_otherMonth: {
    color: theme.colors.textSecondary,
  },
  dayText_today: {
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  dayText_past: {
    color: theme.colors.textSecondary,
  },
  reservationIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reservationCount: {
    fontSize: 10,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 12,
  },
  reservationsList: {
    padding: 16,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  reservationItem: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reservationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    flex: 1,
  },
  reservationHost: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  reservationTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  reservationTimeLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    width: 60,
    marginRight: 8,
  },
  reservationTime: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    flex: 1,
  },
  reservationParticipants: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  crossDayIndicator: {
    fontSize: 12,
    color: theme.colors.warning || '#ff9500',
    fontWeight: 'bold',
    marginTop: 4,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
});
