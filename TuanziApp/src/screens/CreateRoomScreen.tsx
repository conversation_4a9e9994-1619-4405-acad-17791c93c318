import React from 'react';
import { View, Alert, StyleSheet } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../auth/AuthContext';
import { API_URL } from '../api/client';
import { RootStackParamList, EventTemplate } from '../types';
import { theme } from '../styles/theme';
import { TemplateSelector } from '../components/TemplateSelector';

type NavigationProp = StackNavigationProp<RootStackParamList, 'CreateRoom'>;
type CreateRoomRouteProp = RouteProp<RootStackParamList, 'CreateRoom'>;

export const CreateRoomScreen = () => {
    const { token } = useAuth();
    const navigation = useNavigation<NavigationProp>();
    const route = useRoute<CreateRoomRouteProp>();

    // 检查是否为选择模式
    const isSelectMode = route.params?.mode === 'select';
    const returnTo = route.params?.returnTo;

    const handleTemplateSelection = (template: EventTemplate) => {
        // 如果是选择模式，返回到预约页面并传递选中的模板
        if (isSelectMode && returnTo === 'ScheduleRoom') {
            // 获取当前表单状态
            const formState = route.params?.currentFormState;

            // 使用replace而不是navigate，这样会替换当前页面而不是添加新页面到导航栈
            navigation.replace('ScheduleRoom', {
                selectedTemplate: template,
                // 如果有保存的表单状态，则一并传递
                ...(formState && {
                    roomName: formState.roomName,
                    scheduledDateTime: formState.scheduledDateTime,
                    durationHours: formState.durationHours
                })
            });
            return;
        }

        // 否则创建房间
        createRoom(template);
    };

    const createRoom = async (template: EventTemplate) => {
        if (!token) return;
        try {
            const response = await fetch(`${API_URL}/api/rooms/create/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                body: JSON.stringify({ template_id: template.id }),
            });
            const data = await response.json();
            if (response.ok) {
                navigation.replace('Room', { room: data });
            } else {
                Alert.alert('错误', '无法创建房间。');
            }
        } catch (error) {
            Alert.alert('错误', '创建房间时发生错误。');
        }
    };

    return (
        <View style={styles.container}>
            <TemplateSelector
                onSelectTemplate={handleTemplateSelection}
                title={isSelectMode ? "选择一个模板" : "选择一个活动流程"}
                showAsCards={true}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
});
