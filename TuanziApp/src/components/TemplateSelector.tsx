import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, FlatList, Alert, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import { getRoomTemplates } from '../api/calendarApi';
import { theme } from '../styles/theme';

interface Template {
  id: string;
  name: string;
  description: string;
  type: 'system' | 'user';
  creator_username: string;
  steps: any[];
}

interface TemplateSelectorProps {
  onSelectTemplate: (template: Template) => void;
  selectedTemplateId?: string;
  title?: string;
  showAsCards?: boolean; // true: 显示为卡片列表, false: 显示为选择器样式
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onSelectTemplate,
  selectedTemplateId,
  title = '选择一个活动流程',
  showAsCards = true,
}) => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getRoomTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to load templates:', error);
      Alert.alert('错误', '无法加载模板列表。');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const handleSelectTemplate = (template: Template) => {
    onSelectTemplate(template);
  };

  const renderCardItem = ({ item }: { item: Template }) => {
    const isSelected = selectedTemplateId === item.id;
    
    return (
      <TouchableOpacity 
        style={[
          styles.templateCard, 
          isSelected && styles.templateCardSelected
        ]} 
        onPress={() => handleSelectTemplate(item)}
      >
        <View style={styles.templateHeader}>
          <Text style={styles.templateName}>
            {item.type === 'system' ? '🏛️' : '👤'} {item.name}
          </Text>
          <Text style={styles.templateType}>
            {item.type === 'system' ? '系统模板' : '我的模板'}
          </Text>
        </View>
        <Text style={styles.templateDescription}>{item.description}</Text>
        {item.steps && item.steps.length > 0 && (
          <Text style={styles.stepsCount}>
            包含 {item.steps.length} 个环节
          </Text>
        )}
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Text style={styles.selectedText}>✓ 已选择</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderListItem = ({ item }: { item: Template }) => {
    const isSelected = selectedTemplateId === item.id;
    
    return (
      <TouchableOpacity 
        style={[
          styles.listItem, 
          isSelected && styles.listItemSelected
        ]} 
        onPress={() => handleSelectTemplate(item)}
      >
        <View style={styles.listItemContent}>
          <Text style={styles.listItemName}>
            {item.type === 'system' ? '🏛️' : '👤'} {item.name}
          </Text>
          <Text style={styles.listItemDescription}>{item.description}</Text>
        </View>
        {isSelected && (
          <Text style={styles.listItemCheck}>✓</Text>
        )}
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>加载模板...</Text>
      </View>
    );
  }

  if (templates.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>没有可用的模板</Text>
        <TouchableOpacity onPress={fetchTemplates} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>重试</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 分离系统模板和用户模板
  const systemTemplates = templates.filter(t => t.type === 'system');
  const userTemplates = templates.filter(t => t.type === 'user');

  // 合并模板列表，系统模板在前
  const sortedTemplates = [...systemTemplates, ...userTemplates];

  return (
    <View style={styles.container}>
      <FlatList
        data={sortedTemplates}
        renderItem={showAsCards ? renderCardItem : renderListItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={<Text style={styles.title}>{title}</Text>}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  // 卡片样式
  templateCard: {
    backgroundColor: theme.colors.surface,
    padding: 20,
    marginVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  templateCardSelected: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    flex: 1,
  },
  templateType: {
    fontSize: 12,
    color: theme.colors.primary,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: 'bold',
  },
  templateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 5,
    lineHeight: 20,
  },
  stepsCount: {
    fontSize: 12,
    color: theme.colors.primary,
    marginTop: 8,
    fontWeight: '500',
  },
  selectedIndicator: {
    marginTop: 10,
    alignSelf: 'flex-end',
  },
  selectedText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
    fontSize: 12,
  },
  // 列表样式
  listItem: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    marginVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '10',
  },
  listItemContent: {
    flex: 1,
  },
  listItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  listItemDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  listItemCheck: {
    fontSize: 18,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});
