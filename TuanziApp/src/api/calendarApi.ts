// TuanziApp/src/api/calendarApi.ts

import { API_URL } from './client';
import { getToken } from '../auth/storage';
import { CalendarData, ScheduleRoomRequest, Room } from '../types';

/**
 * 日历和预约相关的API客户端
 */

/**
 * 获取日历预约数据
 * @param params 查询参数
 * @returns 日历数据
 */
export const getCalendarData = async (params?: {
  month?: string; // 格式: YYYY-MM
  start_date?: string; // ISO格式
  end_date?: string; // ISO格式
}): Promise<CalendarData> => {
  const token = await getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params?.month) {
    queryParams.append('month', params.month);
  }
  if (params?.start_date) {
    queryParams.append('start_date', params.start_date);
  }
  if (params?.end_date) {
    queryParams.append('end_date', params.end_date);
  }

  const url = `${API_URL}/api/calendar/reservations/?${queryParams.toString()}`;
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 创建预约房间
 * @param scheduleData 预约数据
 * @returns 创建的房间信息
 */
export const scheduleRoom = async (scheduleData: ScheduleRoomRequest): Promise<Room> => {
  const token = await getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  const response = await fetch(`${API_URL}/api/rooms/schedule/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(scheduleData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 获取当前月份的日历数据
 * @returns 当前月份的日历数据
 */
export const getCurrentMonthCalendarData = async (): Promise<CalendarData> => {
  const now = new Date();
  const month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  return getCalendarData({ month });
};

/**
 * 获取指定月份的日历数据
 * @param year 年份
 * @param month 月份 (1-12)
 * @returns 指定月份的日历数据
 */
export const getMonthCalendarData = async (year: number, month: number): Promise<CalendarData> => {
  const monthStr = `${year}-${String(month).padStart(2, '0')}`;
  return getCalendarData({ month: monthStr });
};

/**
 * 获取指定日期范围的日历数据
 * @param startDate 开始日期 (ISO格式)
 * @param endDate 结束日期 (ISO格式)
 * @returns 指定日期范围的日历数据
 */
export const getDateRangeCalendarData = async (
  startDate: string, 
  endDate: string
): Promise<CalendarData> => {
  return getCalendarData({ start_date: startDate, end_date: endDate });
};

/**
 * 验证预约时间是否有效
 * @param scheduledTime 预约时间
 * @returns 是否有效
 */
export const validateScheduleTime = (scheduledTime: Date): boolean => {
  const now = new Date();
  return scheduledTime > now;
};

/**
 * 格式化预约时间为ISO字符串
 * @param date 日期对象
 * @returns ISO格式的时间字符串
 */
export const formatScheduleTime = (date: Date): string => {
  return date.toISOString();
};

/**
 * 解析ISO时间字符串为本地时间
 * @param isoString ISO格式的时间字符串
 * @returns 本地时间的Date对象
 */
export const parseScheduleTime = (isoString: string): Date => {
  return new Date(isoString);
};

/**
 * 获取房间模板列表
 * @returns 房间模板列表
 */
export const getRoomTemplates = async (): Promise<any[]> => {
  const token = await getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  try {
    const response = await fetch(`${API_URL}/api/room-templates/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // 确保返回的是数组
    if (!data.templates || !Array.isArray(data.templates)) {
      return [];
    }

    return data.templates;
  } catch (error) {
    console.error('Error fetching room templates:', error);
    throw error;
  }
};
