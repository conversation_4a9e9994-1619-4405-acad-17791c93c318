/**
 * 用户认证上下文 - 管理用户登录状态和认证信息
 *
 * 功能：
 * - 用户登录/登出
 * - JWT Token管理
 * - 用户信息持久化存储
 * - 自动恢复登录状态
 *
 * 使用方式：
 * 1. 在App根组件包裹AuthProvider
 * 2. 在子组件中使用useAuth()获取认证状态
 */

import React, { createContext, useState, useContext, ReactNode } from 'react';
import { API_URL } from '../api/client';
import * as authStorage from './storage';
// JWT解码功能已在storage.ts中处理

/**
 * 用户信息接口
 */
interface User {
  username: string;                           // 用户名
  subscription_level: 'Free' | 'Pro' | 'Max'; // 订阅等级
}

/**
 * 认证上下文接口定义
 */
interface AuthContextType {
  user: User | null;                          // 当前用户信息
  token: string | null;                       // JWT访问令牌
  login: (username: string, password: string) => Promise<boolean>;  // 登录函数
  logout: () => void;                         // 登出函数
  restoreUser: () => Promise<void>;           // 恢复用户状态函数
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * 认证上下文提供者组件
 * 管理全局认证状态，提供登录/登出功能
 */
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  /**
   * 用户登录函数
   * @param username 用户名
   * @param password 密码
   * @returns Promise<boolean> 登录是否成功
   */
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // 调用后端登录API
      const response = await fetch(`${API_URL}/api/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      const data = await response.json();

      if (response.ok && data.access) {
        // 存储JWT Token到本地存储
        await authStorage.storeToken(data.access);
        setToken(data.access);

        // 解码Token获取用户信息（包括订阅等级）
        const userData = await authStorage.getUser();
        if (userData) {
          setUser({
            username: userData.username,
            subscription_level: userData.subscription_level
          });
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    // 清除本地存储的认证信息
    authStorage.removeToken();
    // 清除状态
    setUser(null);
    setToken(null);
  };

  const restoreUser = async () => {
    const userData = await authStorage.getUser();
    if (userData) {
      setUser({
        username: userData.username,
        subscription_level: userData.subscription_level
      });
      setToken(userData.token);
    }
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, restoreUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};