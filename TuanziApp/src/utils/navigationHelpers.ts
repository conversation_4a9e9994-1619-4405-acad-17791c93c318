// TuanziApp/src/utils/navigationHelpers.ts

import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';

/**
 * 导航辅助工具
 */

/**
 * 安全地导航到指定页面
 * @param navigation 导航对象
 * @param screenName 目标页面名称
 * @param params 页面参数
 */
export const safeNavigate = (
  navigation: NavigationProp<RootStackParamList>,
  screenName: keyof RootStackParamList,
  params?: any
) => {
  try {
    navigation.navigate(screenName as never, params as never);
  } catch (error) {
    console.error('Navigation error:', error);
  }
};

/**
 * 安全地返回上一页
 * @param navigation 导航对象
 * @param fallbackScreen 如果无法返回时的备用页面
 */
export const safeGoBack = (
  navigation: NavigationProp<RootStackParamList>,
  fallbackScreen?: keyof RootStackParamList
) => {
  try {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else if (fallbackScreen) {
      navigation.navigate(fallbackScreen as never);
    }
  } catch (error) {
    console.error('Go back error:', error);
    if (fallbackScreen) {
      navigation.navigate(fallbackScreen as never);
    }
  }
};

/**
 * 重置导航栈到指定页面
 * @param navigation 导航对象
 * @param screenName 目标页面名称
 * @param params 页面参数
 */
export const resetToScreen = (
  navigation: NavigationProp<RootStackParamList>,
  screenName: keyof RootStackParamList,
  params?: any
) => {
  try {
    navigation.reset({
      index: 0,
      routes: [{ name: screenName as never, params }],
    });
  } catch (error) {
    console.error('Reset navigation error:', error);
  }
};
