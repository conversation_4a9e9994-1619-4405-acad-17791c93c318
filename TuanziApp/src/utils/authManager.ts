// TuanziApp/src/utils/authManager.ts

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

export interface AuthResponse {
  error?: string;
  code?: string;
  redirect_to_login?: boolean;
  timeout_minutes?: number;
}

export class AuthManager {
  private static instance: AuthManager;
  private onAuthExpired?: () => void;
  private sessionCheckInterval?: NodeJS.Timeout;
  
  private constructor() {}
  
  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }
  
  /**
   * 设置认证过期回调
   */
  setOnAuthExpired(callback: () => void) {
    this.onAuthExpired = callback;
  }
  
  /**
   * 开始会话检查
   */
  startSessionCheck() {
    // 每5分钟检查一次会话状态
    this.sessionCheckInterval = setInterval(() => {
      this.checkSessionStatus();
    }, 5 * 60 * 1000);
  }
  
  /**
   * 停止会话检查
   */
  stopSessionCheck() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = undefined;
    }
  }
  
  /**
   * 检查会话状态
   */
  private async checkSessionStatus() {
    try {
      const token = await AsyncStorage.getItem('access_token');
      if (!token) {
        this.handleAuthExpired('No token found');
        return;
      }

      // 发送一个轻量级的API请求来检查token状态
      // 需要使用完整的API URL
      const { API_URL } = require('../api/client');
      const response = await fetch(`${API_URL}/api/health-check/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.status === 401) {
        const data = await response.json();
        this.handleAuthResponse(data);
      }
    } catch (error) {
      console.warn('Session check failed:', error);
    }
  }
  
  /**
   * 处理API响应中的认证错误
   */
  handleAuthResponse(response: AuthResponse) {
    if (response.redirect_to_login) {
      switch (response.code) {
        case 'SESSION_TIMEOUT':
          this.handleSessionTimeout(response.timeout_minutes);
          break;
        case 'TOKEN_INVALID':
          this.handleTokenInvalid();
          break;
        case 'AUTH_REQUIRED':
          this.handleAuthRequired();
          break;
        default:
          this.handleAuthExpired(response.error || 'Authentication failed');
      }
    }
  }
  
  /**
   * 处理会话超时
   */
  private handleSessionTimeout(timeoutMinutes?: number) {
    const message = timeoutMinutes
      ? `由于 ${timeoutMinutes} 分钟无活动，您的会话已过期。请重新登录。`
      : '由于长时间无活动，您的会话已过期。请重新登录。';

    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理无效token
   */
  private handleTokenInvalid() {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理需要认证
   */
  private handleAuthRequired() {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理通用认证过期
   */
  private handleAuthExpired(message: string) {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }
  
  /**
   * 重定向到登录页面
   */
  private redirectToLogin() {
    // 清除本地存储的认证信息
    this.clearAuthData();
    
    // 调用认证过期回调
    if (this.onAuthExpired) {
      this.onAuthExpired();
    }
  }
  
  /**
   * 清除认证数据
   */
  private async clearAuthData() {
    try {
      // 使用与AuthContext相同的key名称
      await AsyncStorage.multiRemove([
        'access_token',
        'refresh_token',
        'user_data',
        'auth_token', // AuthContext使用的key
      ]);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }
  
  /**
   * 检查API响应是否包含认证错误
   */
  static checkApiResponse(response: any): boolean {
    if (response && response.redirect_to_login) {
      AuthManager.getInstance().handleAuthResponse(response);
      return true;
    }
    return false;
  }
}

/**
 * API请求拦截器
 */
export const createAuthenticatedFetch = (originalFetch: typeof fetch) => {
  return async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    try {
      const response = await originalFetch(input, init);
      
      // 检查认证错误
      if (response.status === 401) {
        try {
          const data = await response.clone().json();
          AuthManager.getInstance().handleAuthResponse(data);
        } catch (e) {
          // 如果无法解析JSON，使用默认处理
          AuthManager.getInstance().handleAuthExpired('Authentication failed');
        }
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  };
};

// 导出单例实例
export const authManager = AuthManager.getInstance();
