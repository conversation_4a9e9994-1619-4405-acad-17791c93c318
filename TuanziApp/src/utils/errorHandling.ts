// TuanziApp/src/utils/errorHandling.ts

import { Alert } from 'react-native';

/**
 * 网络错误类型
 */
export enum NetworkErrorType {
  TIMEOUT = 'TIMEOUT',
  CONNECTION = 'CONNECTION',
  SERVER = 'SERVER',
  UNAUTHORIZED = 'UNAUTHORIZED',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 网络错误
 */
export class NetworkError extends Error {
  type: NetworkErrorType;
  statusCode?: number;
  details?: any;

  constructor(
    message: string,
    type: NetworkErrorType = NetworkErrorType.UNKNOWN,
    statusCode?: number,
    details?: any
  ) {
    super(message);
    this.name = 'NetworkError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * 处理API响应错误
 * @param response Fetch API响应对象
 * @returns 解析后的错误对象
 */
export const handleApiError = async (response: Response): Promise<NetworkError> => {
  let errorMessage = `请求失败 (${response.status})`;
  let errorType = NetworkErrorType.UNKNOWN;
  let errorDetails = null;

  try {
    const errorData = await response.json();
    errorMessage = errorData.error || errorData.detail || errorData.message || errorMessage;
    errorDetails = errorData;
  } catch (e) {
    // 无法解析JSON，使用默认错误消息
  }

  // 根据状态码确定错误类型
  switch (response.status) {
    case 401:
      errorType = NetworkErrorType.UNAUTHORIZED;
      errorMessage = '未授权，请重新登录';
      break;
    case 403:
      errorType = NetworkErrorType.UNAUTHORIZED;
      errorMessage = '没有权限执行此操作';
      break;
    case 404:
      errorType = NetworkErrorType.NOT_FOUND;
      errorMessage = '请求的资源不存在';
      break;
    case 400:
    case 422:
      errorType = NetworkErrorType.VALIDATION;
      break;
    case 500:
    case 502:
    case 503:
    case 504:
      errorType = NetworkErrorType.SERVER;
      errorMessage = '服务器错误，请稍后重试';
      break;
    default:
      if (response.status >= 500) {
        errorType = NetworkErrorType.SERVER;
      }
      break;
  }

  return new NetworkError(errorMessage, errorType, response.status, errorDetails);
};

/**
 * 显示网络错误提示
 * @param error 错误对象
 * @param customTitle 自定义标题
 */
export const showNetworkError = (error: any, customTitle?: string): void => {
  let title = customTitle || '错误';
  let message = '发生未知错误，请重试';

  if (error instanceof NetworkError) {
    switch (error.type) {
      case NetworkErrorType.TIMEOUT:
        title = '连接超时';
        message = '请求超时，请检查网络连接并重试';
        break;
      case NetworkErrorType.CONNECTION:
        title = '连接错误';
        message = '无法连接到服务器，请检查网络连接';
        break;
      case NetworkErrorType.UNAUTHORIZED:
        title = '未授权';
        message = error.message;
        break;
      case NetworkErrorType.NOT_FOUND:
        title = '未找到';
        message = error.message;
        break;
      case NetworkErrorType.VALIDATION:
        title = '验证错误';
        message = error.message;
        break;
      case NetworkErrorType.SERVER:
        title = '服务器错误';
        message = error.message;
        break;
      default:
        message = error.message || message;
        break;
    }
  } else if (error instanceof Error) {
    message = error.message;
  }

  Alert.alert(title, message);
};
