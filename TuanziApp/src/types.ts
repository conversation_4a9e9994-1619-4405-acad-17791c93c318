// src/types.ts

// --- Data Structure Types ---

// Room status constants
export const ROOM_STATUS = {
  WAITING: 'WAITING',
  IN_PROGRESS: 'IN_PROGRESS',
  FINISHED: 'FINISHED',
} as const;

export type RoomStatus = typeof ROOM_STATUS[keyof typeof ROOM_STATUS];

// Step type constants
export const STEP_TYPES = {
  GAME_PICTIONARY: 'GAME_PICTIONARY',
  FREE_CHAT: 'FREE_CHAT',
} as const;

export type StepType = typeof STEP_TYPES[keyof typeof STEP_TYPES];

export type Room = {
  id: number;
  room_code: string;
  host: string;
  participants: string[];
  status: RoomStatus;
};

export type Message = {
  sender: string;
  message: string;
  timestamp?: string;
};

export type EventStep = {
    id: number;
    name: string;
    order: number;
    step_type: StepType;
    configuration: Record<string, unknown>;
    duration: number;
};

export type EventTemplate = {
    id: string;
    name: string;
    description: string;
    type: 'system' | 'user';
    creator_username: string;
    created_at: string;
    steps: EventStep[];
};

// --- WebSocket Message Types ---

export const WS_MESSAGE_TYPES = {
  STEP_STARTED: 'step_started',
  ROUND_OVER: 'round_over',
  EVENT_FINISHED: 'event_finished',
  ERROR: 'error',
  CHAT_MESSAGE: 'chat_message',
  DRAWING_DATA: 'drawing_data',
  STEP_TIMEOUT: 'step_timeout',
} as const;

export type WSMessageType = typeof WS_MESSAGE_TYPES[keyof typeof WS_MESSAGE_TYPES];

export interface WSMessage<T = unknown> {
  type: WSMessageType;
  payload: T;
}

export interface StepStartedPayload {
  step_info: EventStep;
  room_status: RoomStatus;
  drawer?: string;
  word?: string;
  duration?: number;
}

export interface RoundOverPayload {
  winner?: string;
  word: string;
  room_status: RoomStatus;
  scores: Record<string, number>;
  timeout?: boolean;
}

export interface ChatMessagePayload {
  sender: string;
  message: string;
}

export interface DrawingDataPayload {
  path_data: {
    id: string;
    path: string;
    color: string;
  };
}

// 你画我猜游戏状态
export interface PictionaryState {
  drawer: string;
  word: string;
  duration?: number;
}

export interface ErrorPayload {
  message: string;
}

// --- User and Permission Types ---

export const USER_ROLES = {
  HOST: 'host',
  ADMIN: 'admin', // Future use
  PARTICIPANT: 'participant',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

export interface User {
  username: string;
  role?: UserRole;
  subscription_level?: 'Free' | 'Pro' | 'Max';
}


// --- Navigation Param List ---
// This defines all possible screens and the parameters they expect.

// --- Calendar and Reservation Types ---

export interface Reservation {
  id: number;
  room_code: string;
  name: string;
  host_username: string;
  start_time: string;
  end_time: string;
  duration_hours: number;
  max_participants: number;
  current_participants: number;
  template_id?: number;
  template_name?: string;
  created_at: string;
}

export interface CalendarData {
  reservations: Reservation[];
  query_range: {
    start_date: string;
    end_date: string;
  };
  total_count: number;
}

export interface ScheduleRoomRequest {
  name: string;
  template_id: string; // 改为string以支持 'system_1' 或 'user_1' 格式
  scheduled_start_time: string;
  duration_hours: number;
}

export type RootStackParamList = {
  Home: undefined;
  Register: undefined;
  Login: undefined;
  CreateRoom: {
    mode?: 'create' | 'select';
    returnTo?: string;
    currentFormState?: {
      roomName: string;
      scheduledDateTime: string;
      durationHours: number;
    };
  };
  ScheduleRoom: {
    selectedTemplate?: EventTemplate;
    selectedDate?: string;
    roomName?: string;
    scheduledDateTime?: string;
    durationHours?: number;
  };
  Room: { room: Room };
  EventDesigner: undefined;
  CreateTemplate: undefined;
  TemplateDetail: { templateId: number };
  AddStep: { templateId: number };
  EditStep: { stepId: number };
  Subscription: undefined;
  Calendar: undefined;
  ScheduleRoom: { selectedDate?: string };
};
