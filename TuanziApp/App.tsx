import 'react-native-gesture-handler';

import React, { useEffect } from 'react';
import { AuthProvider } from './src/auth/AuthContext';
import { SubscriptionProvider } from './src/contexts/SubscriptionContext';
import { RootNavigator } from './src/navigation';
import { createAuthenticatedFetch } from './src/utils/authManager';

// 设置全局fetch拦截器
const originalFetch = global.fetch;
global.fetch = createAuthenticatedFetch(originalFetch);

const App = () => {
  return (
    <AuthProvider>
      <SubscriptionProvider>
        <RootNavigator />
      </SubscriptionProvider>
    </AuthProvider>
  );
};

export default App;
