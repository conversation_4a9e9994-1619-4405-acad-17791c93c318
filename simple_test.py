#!/usr/bin/env python
"""
简单的bug修复验证
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

def test_imports():
    """测试导入是否正常"""
    print("测试导入...")
    
    try:
        from core.models import Room, SystemTemplate, TemplateManager
        from core.utils import advance_to_next_step, StepInfo
        print("✓ 导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_step_info():
    """测试StepInfo类"""
    print("测试StepInfo类...")
    
    try:
        from core.utils import StepInfo
        
        step = StepInfo(
            order=1,
            step_type='FREE_CHAT',
            name='测试步骤',
            duration=300,
            configuration={'test': True}
        )
        
        assert step.order == 1
        assert step.step_type == 'FREE_CHAT'
        assert step.name == '测试步骤'
        assert step.duration == 300
        assert step.configuration == {'test': True}
        
        print("✓ StepInfo类测试通过")
        return True
    except Exception as e:
        print(f"✗ StepInfo类测试失败: {e}")
        return False

def test_system_template():
    """测试系统模板"""
    print("测试系统模板...")
    
    try:
        from core.models import SystemTemplate
        
        # 创建测试模板
        import uuid
        template_name = f'测试模板_{uuid.uuid4().hex[:8]}'
        template = SystemTemplate.objects.create(
            name=template_name,
            description='测试描述',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '测试步骤',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 300
                    }
                ]
            }
        )
        
        steps = template.get_steps()
        assert len(steps) == 1
        assert steps[0]['name'] == '测试步骤'
        
        print("✓ 系统模板测试通过")
        
        # 清理
        template.delete()
        return True
    except Exception as e:
        print(f"✗ 系统模板测试失败: {e}")
        return False

def test_template_manager():
    """测试模板管理器"""
    print("测试模板管理器...")
    
    try:
        from django.contrib.auth import get_user_model
        from core.models import TemplateManager, SystemTemplate
        
        User = get_user_model()
        
        # 创建测试用户
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'subscription_level': User.SUBSCRIPTION_FREE
            }
        )
        
        # 创建系统模板
        import uuid
        template_name = f'测试模板2_{uuid.uuid4().hex[:8]}'
        template = SystemTemplate.objects.create(
            name=template_name,
            description='测试描述2',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )
        
        # 测试模板ID解析
        template_obj, template_type = TemplateManager.get_template_by_id(f'system_{template.id}', user)
        
        assert template_obj is not None
        assert template_type == 'system'
        assert template_obj.name == template_name
        
        print("✓ 模板管理器测试通过")
        
        # 清理
        template.delete()
        if created:
            user.delete()
        return True
    except Exception as e:
        print(f"✗ 模板管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始简单验证测试...\n")
    
    tests = [
        test_imports,
        test_step_info,
        test_system_template,
        test_template_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
