#!/usr/bin/env python
"""
最终的bug修复验证
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

def test_system_template_step_advance():
    """测试系统模板步骤推进"""
    print("=== 测试系统模板步骤推进 ===")
    
    from django.contrib.auth import get_user_model
    from core.models import Room, SystemTemplate, RoomParticipant, RoomState
    from core.utils import advance_to_next_step_sync, StepInfo
    import uuid
    
    User = get_user_model()
    
    # 创建测试用户
    user, created = User.objects.get_or_create(
        username='test_step_user',
        defaults={
            'subscription_level': User.SUBSCRIPTION_FREE
        }
    )
    
    # 创建系统模板
    template_name = f'步骤测试模板_{uuid.uuid4().hex[:8]}'
    template = SystemTemplate.objects.create(
        name=template_name,
        description='用于测试步骤推进的模板',
        required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
        template_config={
            'steps': [
                {
                    'id': 'step_1',
                    'name': '自由讨论',
                    'step_type': 'FREE_CHAT',
                    'order': 1,
                    'duration': 300,
                    'configuration': {}
                },
                {
                    'id': 'step_2',
                    'name': '你画我猜',
                    'step_type': 'GAME_PICTIONARY',
                    'order': 2,
                    'duration': 600,
                    'configuration': {'rounds': 5}
                }
            ]
        }
    )
    
    # 创建房间
    room = Room.objects.create(
        room_code='STEP01',
        host=user,
        system_template=template,
        status=RoomState.READY,
        current_step_order=0
    )
    
    print(f"✓ 创建房间: {room.room_code}")
    print(f"  关联系统模板: {room.system_template.name}")
    print(f"  初始步骤顺序: {room.current_step_order}")
    
    # 测试推进到第一个步骤
    print("\n--- 推进到第一个步骤 ---")
    next_step = advance_to_next_step_sync(room)
    
    if next_step and isinstance(next_step, StepInfo):
        print(f"✓ 成功推进到步骤 {next_step.order}: {next_step.name}")
        print(f"  步骤类型: {next_step.step_type}")
        print(f"  持续时间: {next_step.duration}秒")
        
        # 验证房间状态
        room.refresh_from_db()
        print(f"  房间当前步骤: {room.current_step_order}")
        print(f"  房间状态: {room.status}")
        
        if room.current_step_order == 1 and room.status == RoomState.IN_PROGRESS:
            print("✓ 房间状态更新正确")
        else:
            print("✗ 房间状态更新错误")
            return False
    else:
        print("✗ 推进第一个步骤失败")
        return False
    
    # 测试推进到第二个步骤
    print("\n--- 推进到第二个步骤 ---")
    next_step = advance_to_next_step_sync(room)
    
    if next_step and isinstance(next_step, StepInfo):
        print(f"✓ 成功推进到步骤 {next_step.order}: {next_step.name}")
        print(f"  配置: {next_step.configuration}")
        
        # 验证房间状态
        room.refresh_from_db()
        if room.current_step_order == 2:
            print("✓ 房间步骤顺序正确")
        else:
            print("✗ 房间步骤顺序错误")
            return False
    else:
        print("✗ 推进第二个步骤失败")
        return False
    
    # 测试推进超出最后步骤
    print("\n--- 尝试推进超出最后步骤 ---")
    next_step = advance_to_next_step_sync(room)
    
    if next_step is None:
        room.refresh_from_db()
        print(f"✓ 正确处理：没有更多步骤，房间状态: {room.status}")
        if room.status == RoomState.ENDED:
            print("✓ 房间状态正确设置为ENDED")
        else:
            print("✗ 房间状态应该是ENDED")
            return False
    else:
        print("✗ 应该返回None表示没有更多步骤")
        return False
    
    # 清理
    room.delete()
    template.delete()
    if created:
        user.delete()
    
    print("\n✓ 系统模板步骤推进测试通过！")
    return True

def test_template_api():
    """测试模板API"""
    print("\n=== 测试模板API ===")
    
    from rest_framework.test import APIClient
    from django.contrib.auth import get_user_model
    from core.models import SystemTemplate
    import uuid
    
    User = get_user_model()
    client = APIClient()
    
    # 创建测试用户
    user, created = User.objects.get_or_create(
        username='test_api_user',
        defaults={
            'subscription_level': User.SUBSCRIPTION_FREE
        }
    )
    
    # 登录
    client.force_authenticate(user=user)
    
    # 创建系统模板
    template_name = f'API测试模板_{uuid.uuid4().hex[:8]}'
    template = SystemTemplate.objects.create(
        name=template_name,
        description='用于API测试的模板',
        required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
        template_config={'steps': []}
    )
    
    # 测试获取模板列表
    response = client.get('/api/room-templates/')
    
    if response.status_code == 200:
        data = response.json()
        templates = data.get('templates', [])
        print(f"✓ 成功获取模板列表，共 {len(templates)} 个模板")
        
        # 检查是否包含我们创建的模板
        found = False
        for t in templates:
            if t.get('name') == template_name:
                found = True
                print(f"✓ 找到测试模板: {t['name']} (ID: {t['id']})")
                break
        
        if not found:
            print("✗ 未找到测试模板")
            return False
    else:
        print(f"✗ 获取模板列表失败: {response.status_code}")
        return False
    
    # 测试使用系统模板创建房间
    response = client.post('/api/rooms/create/', {
        'template_id': f'system_{template.id}'
    })
    
    if response.status_code == 201:
        room_data = response.json()
        print(f"✓ 成功使用系统模板创建房间: {room_data['room_code']}")
    else:
        print(f"✗ 使用系统模板创建房间失败: {response.status_code}")
        return False
    
    # 清理
    template.delete()
    if created:
        user.delete()
    
    print("✓ 模板API测试通过！")
    return True

def main():
    """主函数"""
    print("开始最终bug修复验证...\n")
    
    tests = [
        test_system_template_step_advance,
        test_template_api
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有bug修复验证通过！")
        print("\n修复的问题:")
        print("1. ✅ 系统模板房间可以正常推进步骤")
        print("2. ✅ 模板ID格式支持字符串格式")
        print("3. ✅ 前端模板API返回正确数据")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == '__main__':
    sys.exit(main())
